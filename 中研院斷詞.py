# 檔案名稱: my_segmenter.py (修正版)
# 描述: 一個使用 ckip-transformers 進行中文斷詞的命令列應用程式。

import os
import logging
import re
from ckip_transformers.nlp import CkipWordSegmenter, CkipPosTagger
import torch
import sys

# 隱藏 transformers 的進度條和警告訊息
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
logging.getLogger("transformers").setLevel(logging.ERROR)
logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)

def load_custom_words(file_path="custom_words.txt"):
    """
    載入自訂詞彙檔案
    """
    custom_words = set()
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word and not word.startswith('#'):  # 忽略空行和註解
                        custom_words.add(word)
            print(f"✅ 載入了 {len(custom_words)} 個自訂詞彙")
        else:
            print(f"📝 未找到自訂詞彙檔案 {file_path}，將使用預設斷詞")
    except Exception as e:
        print(f"⚠️ 載入自訂詞彙時發生錯誤：{e}")

    return custom_words

def apply_custom_words(text, custom_words):
    """
    在文本中預先標記自訂詞彙，避免被錯誤切分
    """
    if not custom_words:
        return text

    # 按詞長度排序，長詞優先處理
    sorted_words = sorted(custom_words, key=len, reverse=True)

    # 使用特殊標記包圍自訂詞
    for word in sorted_words:
        # 使用正則表達式確保完整匹配
        pattern = re.escape(word)
        replacement = f"<CUSTOM>{word}</CUSTOM>"
        text = re.sub(pattern, replacement, text)

    return text

def restore_custom_words(segmented_words):
    """
    還原被特殊標記包圍的自訂詞
    """
    restored_words = []
    for word in segmented_words:
        if word.startswith('<CUSTOM>') and word.endswith('</CUSTOM>'):
            # 移除標記，還原原始詞彙
            restored_word = word[8:-9]  # 移除 <CUSTOM> 和 </CUSTOM>
            restored_words.append(restored_word)
        elif '<CUSTOM>' in word or '</CUSTOM>' in word:
            # 處理被部分切分的情況
            cleaned_word = word.replace('<CUSTOM>', '').replace('</CUSTOM>', '')
            if cleaned_word:
                restored_words.append(cleaned_word)
        else:
            restored_words.append(word)

    return restored_words

def filter_by_pos(words, pos_tags):
    """
    根據詞性標註過濾詞彙。
    移除詞性為 DE（的）和 COMMACATEGORY（逗號）的詞彙。
    """
    # ★ 定義要過濾掉的詞性（不要標點符號）
    excluded_pos = {'DE', 'COMMACATEGORY', 'PERIODCATEGORY', 'WHITESPACE',
                   'QUESTIONCATEGORY', 'EXCLAMATIONCATEGORY', 'SEMICOLONCATEGORY',
                   'COLONCATEGORY', 'PARENTHESISCATEGORY', 'DASHCATEGORY'}

    filtered_words = []
    for word, pos in zip(words, pos_tags):
        # ★ 只保留不在排除列表中的詞性（過濾標點符號）
        if pos not in excluded_pos:
            filtered_words.append(word)

    return filtered_words

def filter_single_char_words(words):
    """
    ★ 過濾掉單字詞彙（只保留兩個字以上的詞）
    """
    filtered_words = []
    for word in words:
        # ★ 只保留長度大於1的詞彙（不要一個字的詞）
        if len(word) > 1:
            filtered_words.append(word)

    return filtered_words

def initialize_models():
    """
    初始化斷詞和詞性標註模型並顯示所使用的硬體裝置。
    這一步會比較花時間，因為需要載入龐大的語言模型。
    """
    print("="*50)
    print("1. 正在初始化斷詞和詞性標註模型...")
    print("   這可能需要幾十秒到數分鐘，請耐心等候。")
    print("="*50)

    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        gpu_id = 0 if device == "cuda" else -1

        print("正在載入斷詞模型...")
        ws_driver = CkipWordSegmenter(model="bert-base", device=gpu_id)

        print("正在載入詞性標註模型...")
        pos_driver = CkipPosTagger(model="bert-base", device=gpu_id)

        print(f"\n✅ 模型初始化完成！將使用 [{device.upper()}] 進行運算。")
        return ws_driver, pos_driver
    except Exception as e:
        print(f"\n❌ 模型初始化失敗！錯誤訊息：{e}")
        print("請確認您已正確安裝 PyTorch 且網路連線正常。")
        sys.exit(1)

def main():
    """
    主應用程式迴圈。
    """
    ws_driver, pos_driver = initialize_models()

    # 載入自訂詞彙
    custom_words = load_custom_words()

    print("\n" + "="*50)
    print("--- 歡迎使用 CKIP 中文斷詞與詞性標註工具 ---")
    print("👉 請輸入任何您想斷詞的句子。")
    print("👉 輸入 'exit' 或 'quit' 即可離開程式。")
    print("👉 輸入 'add:詞彙' 可以臨時添加自訂詞。")
    print("★ 將自動過濾 DE（的）和標點符號。")
    print("★ 將自動過濾單字詞彙（只保留兩字以上）。")
    if custom_words:
        print(f"📚 已載入 {len(custom_words)} 個自訂詞彙")
    print("="*50)

    while True:
        try:
            user_input = input("\n請輸入句子 > ")

            if user_input.lower() in ['exit', 'quit', '離開']:
                print("感謝您的使用，程式即將關閉。")
                break

            # 處理添加自訂詞的指令
            if user_input.startswith('add:'):
                new_word = user_input[4:].strip()
                if new_word:
                    custom_words.add(new_word)
                    print(f"✅ 已添加自訂詞：{new_word}")
                    print(f"📚 目前共有 {len(custom_words)} 個自訂詞彙")
                else:
                    print("⚠️ 請輸入要添加的詞彙，格式：add:詞彙")
                continue

            if not user_input.strip():
                print("⚠️ 請勿輸入空白內容。")
                continue

            print("⏳ 正在進行斷詞和詞性標註...")

            # 預處理：標記自訂詞彙
            processed_text = apply_custom_words(user_input, custom_words)

            # 進行斷詞
            ws_result = ws_driver([processed_text])
            raw_segmented_words = ws_result[0]

            # 還原自訂詞彙
            segmented_words = restore_custom_words(raw_segmented_words)

            # 進行詞性標註
            pos_result = pos_driver([segmented_words])
            pos_tags = pos_result[0]

            # ★ 根據詞性過濾詞彙（移除標點符號和 DE）
            filtered_words = filter_by_pos(segmented_words, pos_tags)

            # ★ 過濾單字詞彙（只保留兩字以上的詞）
            filtered_words = filter_single_char_words(filtered_words)

            formatted_output = " | ".join(filtered_words)

            print("\n【★ 過濾後斷詞結果（無標點符號、無單字詞）】")
            print(f"➡️  {formatted_output}")

            # 顯示完整的詞性標註結果供參考
            print("\n【完整詞性標註結果】")
            pos_output = " | ".join([f"{word}({pos})" for word, pos in zip(segmented_words, pos_tags)])
            print(f"📝 {pos_output}")

        except (KeyboardInterrupt, EOFError):
            print("\n偵測到中斷指令，程式已結束。")
            break
        except Exception as e:
            print(f"\n程式發生未預期的錯誤: {e}")
            break

if __name__ == "__main__":
    main()