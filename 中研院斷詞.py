# 檔案名稱: my_segmenter.py (修正版)
# 描述: 一個使用 ckip-transformers 進行中文斷詞的命令列應用程式。

import os
import logging
import string
import re
from ckip_transformers.nlp import CkipWordSegmenter, CkipPosTagger
import torch
import sys

# 隱藏 transformers 的進度條和警告訊息
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
logging.getLogger("transformers").setLevel(logging.ERROR)
logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)

def filter_by_pos(words, pos_tags):
    """
    根據詞性標註過濾詞彙。
    移除詞性為 DE（的）和 COMMACATEGORY（逗號）的詞彙。
    """
    # 定義要過濾掉的詞性
    excluded_pos = {'DE', 'COMMACATEGORY', 'PERIODCATEGORY', 'WHITESPACE'}

    filtered_words = []
    for word, pos in zip(words, pos_tags):
        # 只保留不在排除列表中的詞性
        if pos not in excluded_pos:
            filtered_words.append(word)

    return filtered_words

def initialize_models():
    """
    初始化斷詞和詞性標註模型並顯示所使用的硬體裝置。
    這一步會比較花時間，因為需要載入龐大的語言模型。
    """
    print("="*50)
    print("1. 正在初始化斷詞和詞性標註模型...")
    print("   這可能需要幾十秒到數分鐘，請耐心等候。")
    print("="*50)

    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        gpu_id = 0 if device == "cuda" else -1

        print("正在載入斷詞模型...")
        ws_driver = CkipWordSegmenter(model="bert-base", device=gpu_id)

        print("正在載入詞性標註模型...")
        pos_driver = CkipPosTagger(model="bert-base", device=gpu_id)

        print(f"\n✅ 模型初始化完成！將使用 [{device.upper()}] 進行運算。")
        return ws_driver, pos_driver
    except Exception as e:
        print(f"\n❌ 模型初始化失敗！錯誤訊息：{e}")
        print("請確認您已正確安裝 PyTorch 且網路連線正常。")
        sys.exit(1)

def main():
    """
    主應用程式迴圈。
    """
    ws_driver, pos_driver = initialize_models()

    print("\n" + "="*50)
    print("--- 歡迎使用 CKIP 中文斷詞與詞性標註工具 ---")
    print("👉 請輸入任何您想斷詞的句子。")
    print("👉 輸入 'exit' 或 'quit' 即可離開程式。")
    print("👉 將自動過濾 DE（的）和標點符號。")
    print("="*50)

    while True:
        try:
            user_input = input("\n請輸入句子 > ")

            if user_input.lower() in ['exit', 'quit', '離開']:
                print("感謝您的使用，程式即將關閉。")
                break

            if not user_input.strip():
                print("⚠️ 請勿輸入空白內容。")
                continue

            print("⏳ 正在進行斷詞和詞性標註...")

            # 進行斷詞
            ws_result = ws_driver([user_input])
            segmented_words = ws_result[0]

            # 進行詞性標註
            pos_result = pos_driver([segmented_words])
            pos_tags = pos_result[0]

            # 根據詞性過濾詞彙
            filtered_words = filter_by_pos(segmented_words, pos_tags)
            formatted_output = " | ".join(filtered_words)

            print("\n【過濾後斷詞結果】")
            print(f"➡️  {formatted_output}")

            # 顯示完整的詞性標註結果供參考
            print("\n【完整詞性標註結果】")
            pos_output = " | ".join([f"{word}({pos})" for word, pos in zip(segmented_words, pos_tags)])
            print(f"📝 {pos_output}")

        except (KeyboardInterrupt, EOFError):
            print("\n偵測到中斷指令，程式已結束。")
            break
        except Exception as e:
            print(f"\n程式發生未預期的錯誤: {e}")
            break

if __name__ == "__main__":
    main()