# 檔案名稱: my_segmenter.py (修正版)
# 描述: 一個使用 ckip-transformers 進行中文斷詞的命令列應用程式。

import os
import logging
import string
import re
from ckip_transformers.nlp import CkipWordSegmenter
import torch
import sys

# 隱藏 transformers 的進度條和警告訊息
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
logging.getLogger("transformers").setLevel(logging.ERROR)
logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)

def remove_punctuation(words):
    """
    移除斷詞結果中的標點符號。
    包含中文標點符號、英文標點符號和其他符號。
    """
    # 定義要過濾的標點符號（中文 + 英文 + 其他符號）
    chinese_punctuation = "，。！？；：「」『』（）【】《》〈〉、·…—－"
    english_punctuation = string.punctuation  # !"#$%&'()*+,-./:;<=>?@[\]^_`{|}~
    other_symbols = "　\u3000\u00A0"  # 全形空格、不間斷空格等

    all_punctuation = chinese_punctuation + english_punctuation + other_symbols

    # 過濾掉標點符號和空白字串
    filtered_words = []
    for word in words:
        # 移除字詞中的標點符號
        cleaned_word = ''.join(char for char in word if char not in all_punctuation)
        # 只保留非空的字詞
        if cleaned_word.strip():
            filtered_words.append(cleaned_word)

    return filtered_words

def initialize_model():
    """
    初始化模型並顯示所使用的硬體裝置。
    這一步會比較花時間，因為需要載入龐大的語言模型。
    """
    print("="*50)
    print("1. 正在初始化斷詞模型...")
    print("   這可能需要幾十秒到數分鐘，請耐心等候。")
    print("="*50)

    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        gpu_id = 0 if device == "cuda" else -1
        
        # --- 錯誤修正處 ---
        # 將模型名稱從 "bert-base-chinese" 改為 "bert-base"
        ws_driver = CkipWordSegmenter(model="bert-base", device=gpu_id)
        # 您也可以嘗試使用更輕量的模型: model="albert-tiny"
        
        print(f"\n✅ 模型初始化完成！將使用 [{device.upper()}] 進行運算。")
        return ws_driver
    except Exception as e:
        print(f"\n❌ 模型初始化失敗！錯誤訊息：{e}")
        print("請確認您已正確安裝 PyTorch 且網路連線正常。")
        sys.exit(1)

def main():
    """
    主應用程式迴圈。
    """
    ws_driver = initialize_model()

    print("\n" + "="*50)
    print("--- 歡迎使用 CKIP 中文斷詞工具 ---")
    print("👉 請輸入任何您想斷詞的句子。")
    print("👉 輸入 'exit' 或 'quit' 即可離開程式。")
    print("="*50)

    while True:
        try:
            user_input = input("\n請輸入句子 > ")

            if user_input.lower() in ['exit', 'quit', '離開']:
                print("感謝您的使用，程式即將關閉。")
                break
            
            if not user_input.strip():
                print("⚠️ 請勿輸入空白內容。")
                continue

            print("⏳ 正在進行斷詞...")
            ws_result = ws_driver([user_input])

            segmented_words = ws_result[0]
            formatted_output = " | ".join(segmented_words)
            
            print("\n【斷詞結果】")
            print(f"➡️  {formatted_output}")

        except (KeyboardInterrupt, EOFError):
            print("\n偵測到中斷指令，程式已結束。")
            break
        except Exception as e:
            print(f"\n程式發生未預期的錯誤: {e}")
            break

if __name__ == "__main__":
    main()