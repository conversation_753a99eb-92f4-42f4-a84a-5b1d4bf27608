# 自訂詞彙使用範例

## 為什麼需要自訂詞彙？

中文斷詞系統可能會將一些專有名詞、公司名稱、產品名稱錯誤切分。例如：

**沒有自訂詞時：**
```
台積電 → 台 | 積 | 電
人工智慧 → 人工 | 智慧
先傑電腦 → 先 | 傑 | 電腦
```

**使用自訂詞後：**
```
台積電 → 台積電
人工智慧 → 人工智慧  
先傑電腦 → 先傑電腦
```

## 使用方法

### 方法一：編輯 custom_words.txt 檔案

1. 打開 `custom_words.txt` 檔案
2. 每行添加一個詞彙
3. 重新啟動程式

```
# 範例內容
台積電
聯發科技
人工智慧
深度學習
先傑電腦
```

### 方法二：程式執行時動態添加

在程式運行時輸入：
```
請輸入句子 > add:ChatGPT
✅ 已添加自訂詞：ChatGPT
📚 目前共有 25 個自訂詞彙

請輸入句子 > OpenAI 開發了 ChatGPT
⏳ 正在進行斷詞和詞性標註...

【★ 過濾後斷詞結果（無標點符號、無單字詞）】
➡️  OpenAI | 開發 | ChatGPT
```

## 注意事項

1. **詞彙長度**：建議自訂詞長度在 2-10 個字之間
2. **避免重複**：不要添加已經能正確斷詞的常用詞

## 常見問題

**Q: 為什麼添加了自訂詞還是被切分？**
A: 請檢查詞彙是否正確添加到檔案中，並重新啟動程式。

**Q: 可以添加英文詞彙嗎？**
A: 可以，支援中英文混合詞彙。

**Q: 自訂詞會影響詞性標註嗎？**
A: 會的，自訂詞會被當作一個完整的詞進行詞性標註。
