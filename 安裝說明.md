# CKIP 中文斷詞工具 - 安裝說明

## 系統需求

- Python 3.6 或以上版本
- Windows / macOS / Linux

## 安裝步驟

### 1. 安裝 Python 套件

```bash
# 方法一：使用 requirements.txt（推薦）
pip install -r requirements.txt

# 方法二：手動安裝各個套件
pip install ckip-transformers>=0.3.0
pip install torch>=1.5.0
pip install transformers>=3.5.0
```

### 2. GPU 支援（可選）

如果您有 NVIDIA 顯卡並想使用 GPU 加速，請安裝對應的 PyTorch CUDA 版本：

```bash
# CUDA 11.8 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CUDA 12.1 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 3. 驗證安裝

```bash
python -c "import ckip_transformers; print('安裝成功！')"
```

## 使用方式

```bash
python 中研院斷詞.py
```

## 功能特色

- ★ **自動過濾標點符號**：移除 DE（的）、逗號、句號等標點符號
- ★ **自動過濾單字詞**：只保留兩個字以上的詞彙
- **詞性標註**：提供完整的詞性分析
- **自訂詞彙支援**：可載入自訂詞彙檔案，確保特定詞彙不被錯誤切分
- **GPU 支援**：可選擇使用 GPU 加速處理

## 自訂詞彙使用方法

### 1. 使用檔案載入（推薦）

編輯 `custom_words.txt` 檔案：
```
# 自訂詞彙檔案
台積電
人工智慧
深度學習
先傑電腦
```

### 2. 程式執行時動態添加

在程式運行時輸入：
```
add:新詞彙
```

### 自訂詞彙檔案格式

- 每行一個詞彙
- 以 `#` 開頭的行為註解
- 空行會被忽略
- 支援中文、英文、數字混合詞彙

## 注意事項

1. 首次使用時會自動下載模型，可能需要幾分鐘時間
2. 建議網路連線穩定
3. 如果遇到安裝問題，請確認 Python 版本是否符合需求

## 疑難排解

### 常見問題

**Q: 安裝時出現 "No module named 'torch'" 錯誤**
A: 請先安裝 PyTorch：`pip install torch`

**Q: 模型下載失敗**
A: 請檢查網路連線，或稍後再試

**Q: GPU 無法使用**
A: 請確認已安裝 CUDA 和對應的 PyTorch CUDA 版本
